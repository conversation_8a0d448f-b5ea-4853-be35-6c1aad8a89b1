obs = obslua

-- 配置存储
local settings = {
    source_name = "",
    active = false,
    interval_min = 2,
    interval_max = 8,
    
    -- Graillon 3 变声参数
    graillon_enabled = true,
    graillon_filter_name = "Graillon",
    graillon_pitch_min = -3.0,
    graillon_pitch_max = 3.0,
    graillon_formant_min = 80.0,
    graillon_formant_max = 120.0,
    graillon_mix_min = 30.0,
    graillon_mix_max = 70.0,
    
    -- TAL Reverb 混响参数
    tal_enabled = true,
    tal_filter_name = "TAL-Reverb",
    tal_roomsize_min = 20.0,
    tal_roomsize_max = 80.0,
    tal_damping_min = 10.0,
    tal_damping_max = 60.0,
    tal_mix_min = 15.0,
    tal_mix_max = 45.0,
    
    -- TSE 808 失真参数
    tse_enabled = true,
    tse_filter_name = "TSE808",
    tse_drive_min = 20.0,
    tse_drive_max = 70.0,
    tse_tone_min = 30.0,
    tse_tone_max = 80.0,
    tse_level_min = 40.0,
    tse_level_max = 80.0,
    
    debug = true
}

local last_update = 0

-- 日志函数
local function log(message, level)
    if settings.debug then
        obs.script_log(level or obs.LOG_INFO, "[VST控制器] " .. tostring(message))
    end
end

-- 获取所有音频源
local function get_audio_sources()
    local sources = {}
    local all_sources = obs.obs_enum_sources()
    if all_sources then
        for _, src in ipairs(all_sources) do
            local name = obs.obs_source_get_name(src)
            table.insert(sources, name)
            obs.obs_source_release(src)
        end
    end
    return sources
end

-- 控制VST插件参数的通用函数
local function control_vst_parameter(source_name, filter_name, param_name, value)
    local source = obs.obs_get_source_by_name(source_name)
    if not source then
        log("音频源不存在: " .. tostring(source_name), obs.LOG_WARNING)
        return false
    end
    
    local filter = obs.obs_source_get_filter_by_name(source, filter_name)
    if not filter then
        log("VST滤镜不存在: " .. tostring(filter_name), obs.LOG_WARNING)
        obs.obs_source_release(source)
        return false
    end
    
    local filter_settings = obs.obs_source_get_settings(filter)
    
    -- 尝试不同的参数名称格式
    local param_names = {param_name, string.lower(param_name), string.upper(param_name)}
    local success = false
    
    for _, name in ipairs(param_names) do
        obs.obs_data_set_double(filter_settings, name, value)
        success = true
        break
    end
    
    if success then
        obs.obs_source_update(filter, filter_settings)
        log(string.format("设置 %s.%s = %.2f", filter_name, param_name, value))
    end
    
    obs.obs_data_release(filter_settings)
    obs.obs_source_release(filter)
    obs.obs_source_release(source)
    return success
end

-- 控制Graillon变声插件
local function control_graillon()
    if not settings.graillon_enabled then return end
    
    local pitch = math.random() * (settings.graillon_pitch_max - settings.graillon_pitch_min) + settings.graillon_pitch_min
    local formant = math.random() * (settings.graillon_formant_max - settings.graillon_formant_min) + settings.graillon_formant_min
    local mix = math.random() * (settings.graillon_mix_max - settings.graillon_mix_min) + settings.graillon_mix_min
    
    control_vst_parameter(settings.source_name, settings.graillon_filter_name, "pitch", pitch)
    control_vst_parameter(settings.source_name, settings.graillon_filter_name, "formant", formant)
    control_vst_parameter(settings.source_name, settings.graillon_filter_name, "mix", mix)
    
    log(string.format("Graillon: 音调=%.1f, 共振峰=%.1f, 混合=%.1f", pitch, formant, mix))
end

-- 控制TAL混响插件
local function control_tal_reverb()
    if not settings.tal_enabled then return end
    
    local roomsize = math.random() * (settings.tal_roomsize_max - settings.tal_roomsize_min) + settings.tal_roomsize_min
    local damping = math.random() * (settings.tal_damping_max - settings.tal_damping_min) + settings.tal_damping_min
    local mix = math.random() * (settings.tal_mix_max - settings.tal_mix_min) + settings.tal_mix_min
    
    control_vst_parameter(settings.source_name, settings.tal_filter_name, "roomsize", roomsize)
    control_vst_parameter(settings.source_name, settings.tal_filter_name, "damping", damping)
    control_vst_parameter(settings.source_name, settings.tal_filter_name, "mix", mix)
    
    log(string.format("TAL混响: 房间=%.1f, 阻尼=%.1f, 混合=%.1f", roomsize, damping, mix))
end

-- 控制TSE808失真插件
local function control_tse808()
    if not settings.tse_enabled then return end
    
    local drive = math.random() * (settings.tse_drive_max - settings.tse_drive_min) + settings.tse_drive_min
    local tone = math.random() * (settings.tse_tone_max - settings.tse_tone_min) + settings.tse_tone_min
    local level = math.random() * (settings.tse_level_max - settings.tse_level_min) + settings.tse_level_min
    
    control_vst_parameter(settings.source_name, settings.tse_filter_name, "drive", drive)
    control_vst_parameter(settings.source_name, settings.tse_filter_name, "tone", tone)
    control_vst_parameter(settings.source_name, settings.tse_filter_name, "level", level)
    
    log(string.format("TSE808: 驱动=%.1f, 音调=%.1f, 电平=%.1f", drive, tone, level))
end

-- 主要的VST控制函数
local function update_vst_parameters()
    if not settings.source_name or settings.source_name == "" then
        log("未选择音频源", obs.LOG_WARNING)
        return
    end
    
    log("开始更新VST参数...")
    
    -- 随机控制各个插件
    control_graillon()
    control_tal_reverb()
    control_tse808()
    
    -- 设置下次更新时间
    local next_interval = math.random() * (settings.interval_max - settings.interval_min) + settings.interval_min
    last_update = os.clock() + next_interval
    log(string.format("下次更新将在 %.1f 秒后", next_interval))
end

-- 脚本属性界面
function script_properties()
    local props = obs.obs_properties_create()
    
    -- 音频源选择
    local source_list = obs.obs_properties_add_list(props, "source_name", "🎤 选择音频源", 
        obs.OBS_COMBO_TYPE_LIST, obs.OBS_COMBO_FORMAT_STRING)
    
    for _, name in ipairs(get_audio_sources()) do
        obs.obs_property_list_add_string(source_list, name, name)
    end
    
    -- 基本设置
    obs.obs_properties_add_float(props, "interval_min", "⏱️ 最小间隔(秒)", 0.5, 30, 0.1)
    obs.obs_properties_add_float(props, "interval_max", "⏱️ 最大间隔(秒)", 0.5, 30, 0.1)
    obs.obs_properties_add_bool(props, "active", "🎛️ 启用VST自动控制")
    obs.obs_properties_add_bool(props, "debug", "🐛 启用调试日志")
    
    -- Graillon设置组
    local graillon_group = obs.obs_properties_create()
    obs.obs_properties_add_bool(graillon_group, "graillon_enabled", "启用Graillon控制")
    obs.obs_properties_add_text(graillon_group, "graillon_filter_name", "滤镜名称", obs.OBS_TEXT_DEFAULT)
    obs.obs_properties_add_float_slider(graillon_group, "graillon_pitch_min", "音调最小值", -12.0, 12.0, 0.1)
    obs.obs_properties_add_float_slider(graillon_group, "graillon_pitch_max", "音调最大值", -12.0, 12.0, 0.1)
    obs.obs_properties_add_float_slider(graillon_group, "graillon_formant_min", "共振峰最小值", 50.0, 150.0, 1.0)
    obs.obs_properties_add_float_slider(graillon_group, "graillon_formant_max", "共振峰最大值", 50.0, 150.0, 1.0)
    obs.obs_properties_add_float_slider(graillon_group, "graillon_mix_min", "混合最小值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_float_slider(graillon_group, "graillon_mix_max", "混合最大值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_group(props, "graillon_group", "🎭 Graillon 变声设置", obs.OBS_GROUP_NORMAL, graillon_group)
    
    -- TAL Reverb设置组
    local tal_group = obs.obs_properties_create()
    obs.obs_properties_add_bool(tal_group, "tal_enabled", "启用TAL混响控制")
    obs.obs_properties_add_text(tal_group, "tal_filter_name", "滤镜名称", obs.OBS_TEXT_DEFAULT)
    obs.obs_properties_add_float_slider(tal_group, "tal_roomsize_min", "房间大小最小值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_float_slider(tal_group, "tal_roomsize_max", "房间大小最大值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_float_slider(tal_group, "tal_damping_min", "阻尼最小值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_float_slider(tal_group, "tal_damping_max", "阻尼最大值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_float_slider(tal_group, "tal_mix_min", "混合最小值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_float_slider(tal_group, "tal_mix_max", "混合最大值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_group(props, "tal_group", "🌊 TAL Reverb 混响设置", obs.OBS_GROUP_NORMAL, tal_group)

    -- TSE808设置组
    local tse_group = obs.obs_properties_create()
    obs.obs_properties_add_bool(tse_group, "tse_enabled", "启用TSE808控制")
    obs.obs_properties_add_text(tse_group, "tse_filter_name", "滤镜名称", obs.OBS_TEXT_DEFAULT)
    obs.obs_properties_add_float_slider(tse_group, "tse_drive_min", "驱动最小值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_float_slider(tse_group, "tse_drive_max", "驱动最大值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_float_slider(tse_group, "tse_tone_min", "音调最小值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_float_slider(tse_group, "tse_tone_max", "音调最大值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_float_slider(tse_group, "tse_level_min", "电平最小值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_float_slider(tse_group, "tse_level_max", "电平最大值", 0.0, 100.0, 1.0)
    obs.obs_properties_add_group(props, "tse_group", "🔥 TSE808 失真设置", obs.OBS_GROUP_NORMAL, tse_group)

    return props
end

-- 更新设置
function script_update(new_settings)
    -- 基本设置
    settings.source_name = obs.obs_data_get_string(new_settings, "source_name")
    settings.active = obs.obs_data_get_bool(new_settings, "active")
    settings.interval_min = obs.obs_data_get_double(new_settings, "interval_min")
    settings.interval_max = obs.obs_data_get_double(new_settings, "interval_max")
    settings.debug = obs.obs_data_get_bool(new_settings, "debug")

    -- Graillon设置
    settings.graillon_enabled = obs.obs_data_get_bool(new_settings, "graillon_enabled")
    settings.graillon_filter_name = obs.obs_data_get_string(new_settings, "graillon_filter_name")
    settings.graillon_pitch_min = obs.obs_data_get_double(new_settings, "graillon_pitch_min")
    settings.graillon_pitch_max = obs.obs_data_get_double(new_settings, "graillon_pitch_max")
    settings.graillon_formant_min = obs.obs_data_get_double(new_settings, "graillon_formant_min")
    settings.graillon_formant_max = obs.obs_data_get_double(new_settings, "graillon_formant_max")
    settings.graillon_mix_min = obs.obs_data_get_double(new_settings, "graillon_mix_min")
    settings.graillon_mix_max = obs.obs_data_get_double(new_settings, "graillon_mix_max")

    -- TAL设置
    settings.tal_enabled = obs.obs_data_get_bool(new_settings, "tal_enabled")
    settings.tal_filter_name = obs.obs_data_get_string(new_settings, "tal_filter_name")
    settings.tal_roomsize_min = obs.obs_data_get_double(new_settings, "tal_roomsize_min")
    settings.tal_roomsize_max = obs.obs_data_get_double(new_settings, "tal_roomsize_max")
    settings.tal_damping_min = obs.obs_data_get_double(new_settings, "tal_damping_min")
    settings.tal_damping_max = obs.obs_data_get_double(new_settings, "tal_damping_max")
    settings.tal_mix_min = obs.obs_data_get_double(new_settings, "tal_mix_min")
    settings.tal_mix_max = obs.obs_data_get_double(new_settings, "tal_mix_max")

    -- TSE设置
    settings.tse_enabled = obs.obs_data_get_bool(new_settings, "tse_enabled")
    settings.tse_filter_name = obs.obs_data_get_string(new_settings, "tse_filter_name")
    settings.tse_drive_min = obs.obs_data_get_double(new_settings, "tse_drive_min")
    settings.tse_drive_max = obs.obs_data_get_double(new_settings, "tse_drive_max")
    settings.tse_tone_min = obs.obs_data_get_double(new_settings, "tse_tone_min")
    settings.tse_tone_max = obs.obs_data_get_double(new_settings, "tse_tone_max")
    settings.tse_level_min = obs.obs_data_get_double(new_settings, "tse_level_min")
    settings.tse_level_max = obs.obs_data_get_double(new_settings, "tse_level_max")

    -- 确保范围有效
    if settings.interval_min > settings.interval_max then
        settings.interval_min, settings.interval_max = settings.interval_max, settings.interval_min
    end

    log("设置已更新")
end

-- 主循环
function script_tick(seconds)
    if settings.active and os.clock() >= last_update then
        update_vst_parameters()
    end
end

-- 脚本描述
function script_description()
    return [[🎛️ VST三插件自动控制器
==============================
专门控制以下三个VST插件的参数：

🎭 Auburn Sounds Graillon 3
   - 音调变化 (Pitch)
   - 共振峰调节 (Formant)
   - 干湿混合 (Mix)

🌊 TAL-Reverb-4
   - 房间大小 (Room Size)
   - 阻尼控制 (Damping)
   - 混响混合 (Mix)

🔥 TSE 808 v2.0
   - 驱动强度 (Drive)
   - 音调控制 (Tone)
   - 输出电平 (Level)

⚙️ 使用说明：
1. 选择要控制的音频源
2. 设置各插件的滤镜名称
3. 调整参数范围和更新间隔
4. 启用自动控制

⚠️ 注意事项：
- 确保VST插件已正确加载到音频源
- 滤镜名称必须与OBS中显示的完全一致
- 建议先测试小范围参数变化
- 可通过调试日志查看详细信息]]
end
